import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Spa<PERSON><PERSON>, Zap, Globe } from 'lucide-react';
import TypewriterText from './TypewriterText';

interface StreamingData {
  type: 'status' | 'translation_chunk';
  message?: string;
  progress: number;
  batch_number?: number;
  total_batches?: number;
  text?: string;
}

interface StreamingTranslationViewerProps {
  isStreaming: boolean;
  streamingData: StreamingData | null;
  accumulatedText: string;
  onStreamingComplete?: () => void;
}

const StreamingTranslationViewer: React.FC<StreamingTranslationViewerProps> = ({
  isStreaming,
  streamingData,
  accumulatedText,
  onStreamingComplete
}) => {
  const [currentBatchText, setCurrentBatchText] = useState('');
  const [currentBatchNumber, setCurrentBatchNumber] = useState<number | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [completedBatches, setCompletedBatches] = useState<Set<number>>(new Set());

  // Persistent progress state that survives batch completions
  const [persistentProgress, setPersistentProgress] = useState({
    currentBatch: 0,
    totalBatches: 0,
    overallProgress: 0,
    message: 'Processing your document...'
  });

  const scrollRef = useRef<HTMLDivElement>(null);

  // Reset state when streaming starts, but preserve progress when streaming stops
  useEffect(() => {
    if (isStreaming) {
      setCurrentBatchText('');
      setCurrentBatchNumber(null);
      setIsTyping(false);
      setCompletedBatches(new Set());
      setPersistentProgress({
        currentBatch: 0,
        totalBatches: 0,
        overallProgress: 0,
        message: 'Processing your document...'
      });
    } else {
      // When streaming stops (translation complete), show final progress
      setPersistentProgress(prev => ({
        ...prev,
        message: 'Translation completed successfully!',
        overallProgress: 100
      }));
    }
  }, [isStreaming]);

  useEffect(() => {
    // Auto-scroll to bottom when new content is added
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [accumulatedText, currentBatchText]);

  useEffect(() => {
    if (streamingData?.type === 'translation_chunk' &&
        streamingData.text &&
        streamingData.batch_number !== undefined) {

      // Update persistent progress state
      setPersistentProgress({
        currentBatch: streamingData.batch_number,
        totalBatches: streamingData.total_batches || 0,
        overallProgress: streamingData.progress || 0,
        message: streamingData.message || 'Processing your document...'
      });

      // Only process if this batch hasn't been completed yet
      if (!completedBatches.has(streamingData.batch_number)) {
        setCurrentBatchText(streamingData.text);
        setCurrentBatchNumber(streamingData.batch_number);
        setIsTyping(true);
      }
    } else if (streamingData?.type === 'status' && streamingData.message) {
      // Update message for status updates
      setPersistentProgress(prev => ({
        ...prev,
        message: streamingData.message || prev.message,
        overallProgress: streamingData.progress || prev.overallProgress
      }));
    }
  }, [streamingData, completedBatches]);

  const handleTypewriterComplete = useCallback(() => {
    setIsTyping(false);

    // Mark this batch as completed
    if (currentBatchNumber !== null) {
      setCompletedBatches(prev => new Set(prev).add(currentBatchNumber));
    }

    // Notify parent that this batch is complete
    onStreamingComplete?.();

    // Clear the current batch text after a short delay to ensure smooth transition
    setTimeout(() => {
      setCurrentBatchText('');
      setCurrentBatchNumber(null);
    }, 100);
  }, [currentBatchNumber, onStreamingComplete]);

  if (!isStreaming) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Progress Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="relative">
            <Sparkles className="h-6 w-6 text-cyan-400 animate-pulse" />
            <div className="absolute -inset-2 bg-cyan-400/20 rounded-full blur-lg animate-pulse"></div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-cyan-400">AI Translation in Progress</h3>
            <p className="text-sm text-slate-400">
              {persistentProgress.message}
            </p>
          </div>
        </div>

        {persistentProgress.currentBatch > 0 && persistentProgress.totalBatches > 0 && (
          <div className="text-right">
            <div className="text-sm text-slate-300">
              Batch {persistentProgress.currentBatch} of {persistentProgress.totalBatches}
            </div>
            <div className="text-xs text-slate-400">
              {Math.round((persistentProgress.currentBatch / persistentProgress.totalBatches) * 100)}% Complete
            </div>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-slate-700 rounded-full h-3 overflow-hidden">
        <div
          className="bg-gradient-to-r from-cyan-500 to-emerald-500 h-full rounded-full transition-all duration-500 ease-out"
          style={{ width: `${persistentProgress.overallProgress}%` }}
        />
      </div>

      {/* Fixed Streaming Content Container */}
      <div className="bg-white rounded-lg border border-slate-600 p-4 min-h-full">
        <div
          ref={scrollRef}
          className="space-y-4 text-black"
          style={{
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            lineHeight: '1.6'
          }}
        >
          {/* Status Messages */}
          {streamingData?.type === 'status' && (
            <div className="flex items-center gap-2 text-cyan-600 mb-4">
              <Globe className="h-4 w-4 animate-spin" />
              <span className="text-sm font-medium">{streamingData.message}</span>
            </div>
          )}

          {/* Accumulated Text (already typed) */}
          {accumulatedText && (
            <div className="text-gray-800 leading-relaxed whitespace-pre-wrap">
              {accumulatedText}
            </div>
          )}

          {/* Current Batch Being Typed */}
          {streamingData?.type === 'translation_chunk' &&
           currentBatchText &&
           currentBatchNumber !== null &&
           !completedBatches.has(currentBatchNumber) && (
            <div className="border-t border-gray-300 pt-4">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="h-4 w-4 text-emerald-600" />
                <span className="text-sm text-emerald-600 font-medium">
                  Translating Batch {streamingData.batch_number}...
                </span>
              </div>
              <TypewriterText
                text={currentBatchText}
                speed={80} // Faster typing for better UX
                onComplete={handleTypewriterComplete}
                className="text-gray-800 leading-relaxed whitespace-pre-wrap"
              />
            </div>
          )}

          {/* Typing Indicator */}
          {isTyping && (
            <div className="flex items-center gap-2 text-gray-600 mt-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-cyan-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-cyan-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-cyan-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="text-xs">AI is typing...</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StreamingTranslationViewer;
