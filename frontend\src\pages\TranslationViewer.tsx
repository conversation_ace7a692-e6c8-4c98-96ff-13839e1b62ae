
import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowRight, RotateCcw, Globe, Sparkles, Download, Copy } from 'lucide-react';
import { Document, Page, pdfjs } from 'react-pdf';
import { useDocument } from '@/contexts/DocumentContext';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import apiService from '@/services/api';
import Header from '@/components/Header';
import StreamingTranslationViewer from '@/components/StreamingTranslationViewer';

// Set up PDF.js worker - using local version
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

const TranslationViewer = () => {
  const navigate = useNavigate();
  const { uploadedFile, translatedContent, setTranslatedContent } = useDocument();
  const [isTranslating, setIsTranslating] = useState(false);
  const [numPages, setNumPages] = useState<number>();
  // Removed pagination state - using continuous scroll instead
  const translatedFileRef = useRef<string | null>(null);

  // Streaming state
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingData, setStreamingData] = useState<any>(null);
  const [accumulatedText, setAccumulatedText] = useState('');
  const [currentEventSource, setCurrentEventSource] = useState<EventSource | null>(null);
  const [processedBatches, setProcessedBatches] = useState<Set<number>>(new Set());

  // Refs for synchronized scrolling
  const pdfPanelRef = useRef<HTMLDivElement>(null);
  const translationPanelRef = useRef<HTMLDivElement>(null);

  // Helper function to count words in translated content
  const getWordCount = (content: string): number => {
    if (!content) return 0;
    // Strip HTML tags and count words
    const textOnly = content.replace(/<[^>]*>/g, ' ').trim();
    return textOnly.split(/\s+/).filter(word => word.length > 0).length;
  };



  useEffect(() => {
    if (!uploadedFile) {
      navigate('/');
      return;
    }

    // Only auto-start translation if this file hasn't been translated yet
    if (translatedFileRef.current !== uploadedFile.name && !translatedContent) {
      handleTranslation();
    }
  }, [uploadedFile, translatedContent]);

  // Cleanup effect for EventSource
  useEffect(() => {
    return () => {
      if (currentEventSource) {
        currentEventSource.close();
      }
    };
  }, [currentEventSource]);

  const handleTranslation = async () => {
    setIsTranslating(true);
    setIsStreaming(true);
    setAccumulatedText('');
    setStreamingData(null);
    setProcessedBatches(new Set()); // Reset processed batches for new translation

    try {
      // Close any existing EventSource
      if (currentEventSource) {
        currentEventSource.close();
      }

      const eventSource = apiService.translatePdfStream(
        uploadedFile?.name || '',
        // onProgress callback
        (data) => {
          setStreamingData(data);

          if (data.type === 'translation_chunk' && data.text && data.batch_number !== undefined) {
            // Only process if this batch hasn't been processed yet
            setProcessedBatches(prev => {
              if (!prev.has(data.batch_number)) {
                // This is a new batch, allow it to be processed
                return prev;
              }
              // This batch was already processed, don't update streamingData
              return prev;
            });
          }
        },
        // onComplete callback
        (finalContent) => {
          setTranslatedContent(finalContent);
          translatedFileRef.current = uploadedFile?.name || null;
          setIsStreaming(false);
          setIsTranslating(false);

          toast({
            title: "Translation Complete",
            description: "Your document has been successfully translated.",
          });
        },
        // onError callback
        (error) => {
          console.error('Streaming translation error:', error);
          setIsStreaming(false);
          setIsTranslating(false);

          toast({
            title: "Translation Failed",
            description: error,
            variant: "destructive",
          });
        }
      );

      setCurrentEventSource(eventSource);

    } catch (error) {
      console.error('Translation error:', error);
      setIsStreaming(false);
      setIsTranslating(false);

      toast({
        title: "Translation Failed",
        description: error instanceof Error ? error.message : "Failed to translate document",
        variant: "destructive",
      });
    }
  };

  const handleStreamingComplete = () => {
    // When a batch completes typing, add it to accumulated text
    if (streamingData?.type === 'translation_chunk' &&
        streamingData.text &&
        streamingData.batch_number !== undefined) {

      // Only add to accumulated text if this batch hasn't been processed yet
      setProcessedBatches(prev => {
        if (!prev.has(streamingData.batch_number)) {
          // Add the batch text to accumulated text
          setAccumulatedText(prevText => prevText + (prevText ? '\n\n' : '') + streamingData.text);
          // Mark this batch as processed
          return new Set(prev).add(streamingData.batch_number);
        }
        return prev;
      });
    }
  };

  const handleProceedToFeatures = () => {
    navigate('/features');
  };

  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setNumPages(numPages);
  }

  // Removed pagination and zoom functions - using continuous scroll instead

  if (!uploadedFile) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <Header title="Translation Viewer" showBackButton backTo="/">
        <Button
          variant="outline"
          onClick={() => {
            translatedFileRef.current = null; // Reset to allow re-translation
            handleTranslation();
          }}
          disabled={isTranslating}
          className="bg-slate-700 hover:bg-slate-600 text-slate-200 font-medium px-6 py-3 rounded-lg transition-all duration-200 border-slate-600"
        >
          <Globe className="h-4 w-4 mr-2" />
          Translate Again
        </Button>
        <Button
          onClick={handleProceedToFeatures}
          disabled={isTranslating || !translatedContent}
          className="bg-gradient-to-r from-cyan-500 to-emerald-500 hover:from-cyan-600 hover:to-emerald-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 border-0"
        >
          <ArrowRight className="h-4 w-4 mr-2" />
          Proceed to Feature Extraction
        </Button>
      </Header>

      {/* Content */}
      <div className="max-w-[95vw] mx-auto p-4">
        <div className="grid lg:grid-cols-2 gap-4">
          {/* Original PDF Panel */}
          <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-4 transition-all duration-200 hover:bg-slate-800/60">
            <div className="space-y-4">
              <div className="flex items-center justify-between min-h-[60px]">
                <h2 className="text-xl font-semibold text-slate-200">Original Document</h2>
                <div className="flex items-center gap-2 max-w-[300px]">
                  <div className="bg-slate-700/50 rounded-lg px-3 py-2 border border-slate-600 flex items-center min-h-[32px]">
                    <span className="text-sm text-slate-300 font-medium truncate" title={uploadedFile.name}>
                      {uploadedFile.name}
                    </span>
                  </div>
                </div>
              </div>

              {/* Document Info */}
              <div className="flex items-center justify-end py-2">
                <span className="text-slate-400 text-sm">
                  {numPages ? `${numPages} pages` : 'Loading...'}
                </span>
              </div>
              
              <div
                ref={pdfPanelRef}
                className="bg-slate-900/50 rounded-lg p-2 border border-slate-700 h-[calc(100vh-280px)] overflow-auto scroll-smooth scrollbar-thin scrollbar-track-slate-800 scrollbar-thumb-slate-600 hover:scrollbar-thumb-slate-500"
              >
                {/* Fixed PDF Content Container */}
                <div className="bg-white rounded-lg border border-slate-600 p-4 min-h-full">
                  <div className="flex flex-col items-center space-y-2">
                    <Document
                      file={uploadedFile}
                      onLoadSuccess={onDocumentLoadSuccess}
                      loading={
                        <div className="flex items-center justify-center p-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-2 border-cyan-400 border-t-transparent"></div>
                        </div>
                      }
                      error={
                        <div className="text-center p-8 text-slate-400">
                          <p>Failed to load PDF</p>
                          <p className="text-sm">Please try uploading again</p>
                        </div>
                      }
                    >
                      {numPages && Array.from({ length: numPages }, (_, index) => (
                        <div key={index + 1} className="mb-4">
                          <Page
                            pageNumber={index + 1}
                            scale={1.0}
                            className="shadow-sm"
                          />
                        </div>
                      ))}
                    </Document>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Translated Panel */}
          <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 p-4 transition-all duration-200 hover:bg-slate-800/60">
            <div className="space-y-4">
              <div className="flex items-center justify-between min-h-[60px]">
                <h2 className="text-xl font-semibold text-slate-200">Translated Document</h2>
                <div className="flex items-center gap-3 min-h-[40px]">
                  {isTranslating && (
                    <div className="flex items-center gap-2 text-cyan-400 h-[40px]">
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-cyan-400 border-t-transparent"></div>
                      <span className="text-sm">Translating...</span>
                    </div>
                  )}
                  {translatedContent && !isTranslating && (
                    <div className="flex items-center gap-2 h-[40px]">
                        <Button
                          variant="outline"
                          onClick={() => {
                            navigator.clipboard.writeText(translatedContent);
                            toast({
                              title: "Copied to Clipboard",
                              description: "Translated content has been copied to your clipboard.",
                            });
                          }}
                          className="bg-slate-700 hover:bg-slate-600 text-slate-200 font-medium px-4 py-2 rounded-lg transition-all duration-200 border-slate-600 text-sm h-8"
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            const blob = new Blob([translatedContent], { type: 'text/html' });
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `${uploadedFile?.name.replace('.pdf', '_translated.html')}`;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);
                            toast({
                              title: "Download Complete",
                              description: "Translated HTML file has been downloaded.",
                            });
                          }}
                          className="bg-slate-700 hover:bg-slate-600 text-slate-200 font-medium px-4 py-2 rounded-lg transition-all duration-200 border-slate-600 text-sm h-8"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Export HTML
                        </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Translated Document Info - Status and Word Count */}
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  {isTranslating && (
                    <>
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                      <span className="text-sm font-medium text-cyan-400">Translation In Progress</span>
                    </>
                  )}
                  {translatedContent && !isTranslating && (
                    <>
                      <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                      <span className="text-sm font-medium text-emerald-400">Translation Complete</span>
                    </>
                  )}
                </div>
                <span className="text-slate-400 text-sm">
                  {translatedContent ? `${getWordCount(translatedContent)} words` : ''}
                </span>
              </div>

              <div
                ref={translationPanelRef}
                className="bg-slate-900/50 rounded-lg p-2 border border-slate-700 h-[calc(100vh-280px)] overflow-auto scroll-smooth scrollbar-thin scrollbar-track-slate-800 scrollbar-thumb-slate-600 hover:scrollbar-thumb-slate-500"
              >
                {isStreaming ? (
                  <StreamingTranslationViewer
                    isStreaming={isStreaming}
                    streamingData={streamingData}
                    accumulatedText={accumulatedText}
                    onStreamingComplete={handleStreamingComplete}
                  />
                ) : isTranslating ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center space-y-6">
                      <div className="relative">
                        <Sparkles className="h-12 w-12 text-cyan-400 mx-auto animate-pulse" />
                        <div className="absolute -inset-3 bg-cyan-400/20 rounded-full blur-lg animate-pulse"></div>
                      </div>
                      <div className="space-y-2">
                        <p className="text-cyan-400 font-medium text-lg">AI Translation in Progress</p>
                        <p className="text-slate-300 leading-relaxed">Analyzing and translating your document...</p>
                      </div>
                      <div className="w-48 bg-slate-700 rounded-full h-2 mx-auto overflow-hidden">
                        <div className="bg-gradient-to-r from-cyan-500 to-emerald-500 h-full rounded-full animate-pulse w-3/4"></div>
                      </div>
                    </div>
                  </div>
                ) : translatedContent ? (
                  /* Fixed Translation Content Container */
                  <div className="bg-white rounded-lg border border-slate-600 p-4 min-h-full">
                    {translatedContent.trim() ? (
                      <div
                        className="text-black"
                        style={{
                          fontFamily: 'Arial, sans-serif',
                          fontSize: '14px',
                          lineHeight: '1.6'
                        }}
                        dangerouslySetInnerHTML={{ 
                          __html: `
                            <style>
                              body { 
                                margin: 0; 
                                padding: 0; 
                                font-family: Arial, sans-serif; 
                                background: white;
                                color: #374151;
                              }
                              h1, h2, h3, h4, h5, h6 { 
                                color: #1f2937; 
                                margin-top: 1.5em; 
                                margin-bottom: 0.5em; 
                                font-weight: 600;
                              }
                              h1 { font-size: 1.5em; }
                              h2 { font-size: 1.3em; }
                              h3 { font-size: 1.1em; }
                              p { 
                                margin-bottom: 1em; 
                                color: #374151; 
                                line-height: 1.6;
                              }
                              ul, ol { 
                                margin-bottom: 1em; 
                                padding-left: 2em; 
                              }
                              li { 
                                margin-bottom: 0.5em; 
                                line-height: 1.5;
                              }
                              table { 
                                border-collapse: collapse; 
                                width: 100%; 
                                margin-bottom: 1em; 
                                font-size: 0.9em;
                              }
                              th, td { 
                                border: 1px solid #d1d5db; 
                                padding: 8px; 
                                text-align: left; 
                              }
                              th { 
                                background-color: #f3f4f6; 
                                font-weight: bold; 
                                color: #1f2937;
                              }
                              .highlight { 
                                background-color: #fef3c7; 
                                padding: 2px 4px; 
                                border-radius: 3px; 
                              }
                              blockquote {
                                border-left: 4px solid #3b82f6;
                                margin: 1em 0;
                                padding-left: 1em;
                                font-style: italic;
                                color: #6b7280;
                              }
                              code {
                                background-color: #f3f4f6;
                                padding: 2px 4px;
                                border-radius: 3px;
                                font-family: 'Courier New', monospace;
                                font-size: 0.9em;
                              }
                              pre {
                                background-color: #f3f4f6;
                                padding: 1em;
                                border-radius: 6px;
                                overflow-x: auto;
                                margin: 1em 0;
                              }
                              pre code {
                                background: none;
                                padding: 0;
                              }
                            </style>
                            ${translatedContent}
                          `
                        }}
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full text-slate-400">
                        <p>No translated content available</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-slate-400">
                    <p>Translated content will appear here</p>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TranslationViewer;
