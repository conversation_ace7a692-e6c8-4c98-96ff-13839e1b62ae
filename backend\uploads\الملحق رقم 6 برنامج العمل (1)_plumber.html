
    <!DOCTYPE html>
    <html lang='ar' dir='rtl'>
    <head>
    <meta charset='UTF-8'>
    <title>Translated Arabic PDF</title>
    <style>
    body { font-family: Tahoma, sans-serif; font-size: 16px; direction: ltr; padding: 40px; max-width: 1000px; margin: auto; }
    table { width: 100%; border-collapse: collapse; margin: 24px 0; background: #fff; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    th, td { border: 1px solid #ccc; padding: 12px; text-align: left; }
    th { background: #003366; color: white; }
    tr:nth-child(even) { background-color: #f2f2f2; }
    </style>
    </head>
    <body>
    <p>ملحق رقم 2</p>
<h2>برنامج العمل:</h2>
<ol>
<li>
<p>المتطلبات الأساسية للتنفيذ</p>
<ul>
<li>توفير بنية تحتية سحابية لنظام المعلومات الطلابية ونظام القبول وموقع الجامعة الإلكتروني مع الاستفادة من قدرات الحوسبة السحابية لضمان الأداء العالي والتكامل السلس.</li>
<li>نقل قاعدة بيانات Oracle إلى أحدث إصدار متوافق مع تحسينات في الأداء والاستقرار.</li>
<li>نقل بيئة Ellucian Banner 9 من خوادم جامعة الملك عبد العزيز الى البيئة السحابية.</li>
<li>نقل بيئة نظام القبول من خوادم جامعة الملك عبد العزيز الى البيئة السحابية.</li>
<li>نقل بيئة Camundas من خوادم جامعة الملك عبد العزيز الى البيئة السحابية.</li>
<li>نقل بيئة موقع الجامعة الإلكتروني من خوادم جامعة الملك عبد العزيز الى البيئة السحابية.</li>
<li>إنشاء بنية تحتية آمنة وقابلة للتوسع مع إمكانيات التوسع التلقائي لتلبية احتياجات النظام المستقبلية.</li>
<li>تطبيق معايير الأمان والامتثال وفقا لمتطلبات الهيئة الوطنية للأمن السيبراني (NCA) ومعايير ISO 27001 و PCI وسياسات الجامعة.</li>
<li>تحسين حلول النسخ الاحتياطي والتعافي من الكوارث لضمان زمن تعافي (RTO) وانقضاء نقطة الاسترداد (RPO) يساوي صفرًا.</li>
</ul>
</li>
<li>
<p>الخدمات الإضافية الموصى بها</p>
<ul>
<li>تنفيذ البنية التحتية ككود (Iac) لضمان إدارة البنية التحتية بكفاءة، وتقليل الأخطاء التشغيلية، وتحقيق استمرارية في العمليات.</li>
<li>إعداد أنظمة مراقبة وتحليل الأداء مع تنبيهات تلقائية لضمان استقرار النظام والاستجابة السريعة لأي مشكلات.</li>
<li>تصميم إطار عمل لتحسين التكلفة من خلال تحليل استهلاك الموارد وتقديم توصيات لتخصيصها بشكل فعال.</li>
<li>تطوير مسار تلقائي CI/CD مؤتمت لتحديث التطبيقات وتطبيق التصحيحات الأمنية بسلاسة.</li>
<li>تطبيق تشفير البيانات أثناء التخزين والنقل مع إدارة شاملة لمفاتيح التشفير لتعزيز الحماية.</li>
<li>إنشاء إطار عمل لإدارة الهوية والصلاحيات وفقا لمبدأ أقل الصلاحيات الممكنة (Least Privilege Access) لضمان التحكم الدقيق في الوصول إلى البيانات والأنظمة.</li>
<li>وضع نموذج متكامل لحوكمة الخدمات السحابية يشمل إدارة التكاليف وضبط الامتثال التنظيمي.</li>
<li>توفير برامج تدريب ونقل المعرفة لفريق تقنية المعلومات في الجامعة لتعزيز مهارات إدارة وتشغيل البيئة السحابية.</li>
<li>إجراء اختبارات أداء متقدمة وتحسين ضبط النظام لضمان تجربة مستخدم مثالية.</li>
<li>تطوير حلول تكامل سحابية أصلية تربط Ellucian Banner بالأنظمة المؤسسية الأخرى لضمان التشغيل السلس والفعال.</li>
</ul>
</li>
<li>
<p>متطلبات الأمن والامتثال</p>
<ul>
<li>تنفيذ حماية شاملة ضد هجمات الحرمان من الخدمة (DDoS) على مستوى الشبكة والتطبيقات لحماية الأنظمة من التهديدات السيبرانية.</li>
<li>تنفيذ قدرات متقدمة لاستخبارات التهديدات الأمنية مع آليات استجابة تلقائية للكشف الفوري عن الهجمات والتعامل معها بفعالية.</li>
<li>إعداد نظام متكامل لإدارة معلومات الأمن والأحداث (SIEM) لرصد وتحليل الأنشطة المشبوهة في البيئة السحابية.</li>
<li>تطبيق جدار حماية متقدم لتطبيقات الويب (WAF) مع قواعد مخصصة لحماية نظام Ellucian Banner والقبول وموقع الجامعة الإلكتروني من الهجمات الإلكترونية.</li>
<li>إعداد آليات متقدمة لحماية الشبكات وفقا لنهج الدفاع متعدد الطبقات (Defense-in-Depth) لضمان بيئة آمنة ضد التهديدات المحتملة.</li>
<li>تنفيذ المراقبة المستمرة للامتثال التنظيمي وضمان الالتزام بالمتطلبات القانونية والسياسات الداخلية.</li>
<li>إجراء فحص تلقائي دوري للثغرات الأمنية مع تنفيذ إجراءات فورية لمعالجتها وتقليل المخاطر.</li>
<li>تصميم مسار تلقائي CI/CD آمن مع تكامل اختبارات الأمان لضمان تحديثات آمنة وخالية من الثغرات الأمنية.</li>
<li>تنفيذ فحص دوري تلقائي للثغرات الأمنية عبر دمج أدوات IBM Trend Micro, Qualys, QRadar لتحليل الامتثال وتتبع التهديدات السيبرانية ومعالجتها تلقائيا.</li>
<li>فرض المصادقة متعددة العوامل (MFA) على جميع المستخدمين لضمان مستوى أمان أعلى عند الوصول إلى الأنظمة الحساسة.</li>
<li>تنفيذ إدارة صلاحيات دقيقة وفقا لمبدأ التحكم في الوصول المبني على الأدوار (RBAC) لضمان تخصيص الصلاحيات بناءً على وظائف المستخدمين وتقييد الوصول غير المصرح به.</li>
<li>تكامل نظام المصادقة نفاذ (NAFATH) باستخدام (OpenID Connect (OIDC أو SAML 2.0 لضمان تسجيل دخول موحد وآمن لجميع المستخدمين.</li>
<li>تنفيذ VPC Lattice لإنشاء شبكة خدمات مؤمنة بين بيئات التشغيل المختلفة، مما يتيح ربط الخدمات السحابية بطريقة مركزية وآمنة دون الحاجة إلى إدارة تعقيدات الشبكة يدويًا.</li>
<li>استخدام PrivateLink لربط الخدمات السحابية داخليًا دون تعريضها للإنترنت العام، مما يعزز الأمان، يقلل من المخاطر السيبرانية، ويحسن الأداء عبر قنوات اتصال خاصة ومباشرة.</li>
<li>توفير بنية تحتية آمنة تفصل عمليات الـ Virtualization عن بيئة تشغيل النظام من خلال نقل وظائف إدارة الأجهزة الافتراضية إلى Dedicated Hardware وتقليل الاعتماد على الـ Hypervisor التقليدي، مما يعزز الأمان عبر تقليل سطح الهجوم، ويحسن الأداء.</li>
</ul>
</li>
<li>
<p>إدارة المشروع والجدول الزمني</p>
<ul>
<li>تنفيذ إدارة متكاملة للمشروع عبر جميع مراحل الانتقال إلى البيئة السحابية، لضمان التنفيذ الناجح دون تعطيل العمليات.</li>
<li>إعداد هيكل تفصيلي لأعمال المشروع (WBS) مع توزيع الموارد بفعالية على جميع المهام.</li>
<li>وضع إطار شامل لحوكمة المشروع يتضمن لجنة توجيهية، وإجراءات واضحة لاتخاذ القرار.</li>
<li>تطبيق خطة لإدارة المخاطر تتضمن تحليلاً شاملاً للمخاطر ووضع استراتيجيات للتخفيف منها.</li>
<li>عقد اجتماعات أسبوعية لمتابعة تقدم المشروع وإعداد تقارير شهرية موجهة للإدارة التنفيذية لضمان شفافية التنفيذ.</li>
<li>إعداد وتحديث سجلات RAID (المخاطر الإجراءات القضايا، القرارات) لمتابعة تقدم المشروع وتجنب العقبات المحتملة.</li>
<li>إنشاء إجراءات فعالة لإدارة التغيير مع وضع خطط واضحة للتواصل مع جميع الأطراف المعنية</li>
</ul>
</li>
</ol>

    </body>
    </html>
    