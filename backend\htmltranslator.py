import os
import re
import json
from mistralai import Mistral
from dotenv import load_dotenv
from bs4 import BeautifulSoup
from html import escape as escape_html
import tiktoken
import time
# Load API key
load_dotenv()
API_KEY = os.getenv("MISTRAL_API_KEY")
TRANSLATION_MODEL = "mistral-saba-2502"
FORMATTING_MODEL = "codestral-latest"

client = Mistral(api_key=API_KEY)

INPUT_HTML_PATH = "pdfplumber/output_w_pageno3.html"
OUTPUT_HTML_PATH = "translated_document.html"

# --- Tokenizer setup ---
ENCODING = tiktoken.get_encoding("cl100k_base")
TOKEN_LIMIT = 6000

import re

def is_arabic(text):
    return bool(re.search(r'[\u0600-\u06FF]', text))

def contains_arabic(text):
    import re
    return bool(re.search(r'[\u0600-\u06FF]', text))

def extract_readable_text_from_html(html_content):
    """Extract clean, readable text from HTML content for streaming display"""
    soup = BeautifulSoup(html_content, 'html.parser')

    # Remove script and style elements
    for script in soup(["script", "style"]):
        script.decompose()

    # Get text and clean it up
    text = soup.get_text()

    # Clean up whitespace
    lines = (line.strip() for line in text.splitlines())
    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
    text = ' '.join(chunk for chunk in chunks if chunk)

    return text

# --- Token estimation ---
def estimate_tokens(text):
    return len(ENCODING.encode(text))

# --- Batching logic for HTML by <h2> ---
def batch_html_by_tokens(html, max_tokens=TOKEN_LIMIT):
    import re
    parts = re.split(r'(<h2>.*?</h2>)', html, flags=re.DOTALL)
    batches = []
    current = ''
    current_tokens = 0
    for part in parts:
        if not part:
            continue
        part_tokens = estimate_tokens(part)
        if current_tokens + part_tokens > max_tokens and current:
            batches.append(current)
            current = part
            current_tokens = part_tokens
        else:
            current += part
            current_tokens += part_tokens
    if current:
        batches.append(current)
    return batches

def format_translated_html_with_codestral_chunked(translated_html):
    print("[CODESTRAL] Chunking translated HTML for formatting...")

    def chunk_by_h2_tags(html, token_limit=6000):
        parts = re.split(r'(?=<h2>)', html)
        chunks = []
        current_chunk = ""
        for part in parts:
            if not part.strip():
                continue
            if estimate_tokens(current_chunk + part) > token_limit:
                chunks.append(current_chunk)
                current_chunk = part
            else:
                current_chunk += part
        if current_chunk:
            chunks.append(current_chunk)
        return chunks

    def summarize_structure(html):
        soup = BeautifulSoup(html, 'html.parser')
        summary = []
        for tag in soup.find_all(['h1', 'h2', 'h3']):
            summary.append(f"{tag.name.upper()}: {tag.get_text(strip=True)}")
        return "\n".join(summary)

    SYSTEM_PROMPT_TEMPLATE = """
    You are an expert HTML formatter and semantic structure specialist.

    --- Objective ---
    Your task is to clean, reformat, and fix the hierarchy of HTML content that has been translated from Arabic. The goal is to produce clean, readable, professional HTML with proper structure, without altering the actual content meaning.

    --- Rules ---
    1. Ensure there is **exactly one** <h1> tag at the very top of the document.
    2. Replace improper section headings (e.g., <p> or <div> acting as titles) with appropriate semantic headings: use <h2>, <h3>, <h4>, etc.
    3. Preserve all valid and meaningful HTML tags, attributes, and correct nesting.
    4. Fix indentation, line breaks, and structural readability. Remove visual clutter like:
    - Repeated "Page N" headings
    - Duplicate section titles
    - Generic or placeholder headers (e.g., "Translated Arabic PDF", "Page Title")
    5. Translate any remaining Arabic text into clear, fluent English if present.
    6. If this is part of a multi-chunk document:
    - Do NOT repeat <h1> headings.
    - Maintain logical flow and section continuity from previous chunks.
    - Use heading levels consistently and hierarchically (e.g., h2 > h3 > h4).
    - Do not reference or include prior content — only use it to guide structure.

--- Numbered Section Handling ---

 Do NOT turn numbered content into headings *just because it starts with a number*.

 ONLY convert numbered items to headings if:
- The number is clearly part of a **section heading** (e.g., "1. Basic Requirements").
- It is formatted like a section (e.g., <li><h2>...</h2></li>), with **no body content** or description inside the <li>.
- The number and title are meant to introduce a **distinct section**, not a bullet point, condition, or requirement.

 DO NOT promote numbered items to <h2>/<h3> if:
- The item is part of a list of tasks, features, conditions, or requirements.
- The item contains body text, descriptions, or child elements like <ul>, <table>, or <p>.
- The structure reads like a list, not a document outline.

 Keep these as normal lists using:
<ol>
  <li>First requirement</li>
  <li>Second requirement</li>
</ol>

 To determine intent:
- If it looks like a **long numbered checklist or conditions list**, it stays a list.
- If it's a section outline like: "1. Introduction", "2. Scope", "3. Implementation" — then use headings.



    --- Output Requirements ---
    - Return only the cleaned and corrected **HTML**.
    - Do NOT wrap in markdown, code blocks, or any other output format.
    - Ensure the result is **production-quality HTML**, semantically correct and professional.
    """.strip()


    html_chunks = chunk_by_h2_tags(translated_html)
    print(f"[CODESTRAL] Total HTML chunks: {len(html_chunks)}")

    formatted_chunks = []
    prior_summary = ""

    for i, chunk in enumerate(html_chunks):
        print(f"[CODESTRAL] Formatting chunk {i+1}/{len(html_chunks)}...")

        system_prompt = SYSTEM_PROMPT_TEMPLATE
        if prior_summary:
            system_prompt += (
                f"\n\nContext from previous chunks:\n"
                f"{prior_summary}\n\n"
                "Maintain consistent heading hierarchy, avoid repeating <h1>, and continue structure logically. The previous chunk is not to be used or repeated in your output its only for context"
            )

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": chunk}
        ]

        response = client.chat.complete(
            model=FORMATTING_MODEL,
            messages=messages,
            temperature=0.0,
        )
        formatted_chunk = response.choices[0].message.content.strip()
        formatted_chunks.append(formatted_chunk)
        prior_summary = summarize_structure(formatted_chunk)

    print("[CODESTRAL] All chunks formatted.")
    return '\n'.join(formatted_chunks)


def format_translated_html_with_codestral(translated_html):
    print("[CODESTRAL] Calling Codestral to clean and format HTML...")

    SYSTEM_PROMPT = """
You are an expert HTML formatter and structural fixer.

--- Objective ---
Fix all formatting, hierarchy, and structural issues in the HTML translated from Arabic. Do not change content. Focus only on logical structure and tag usage.

--- Rules ---
1. Ensure only one <h1> at the top of the document.
2. Replace <p> or <div> used as section titles with semantic headings like <h2>, <h3>.
3. Preserve all valid HTML tags, attributes, and nesting.
4. Clean indentation and remove visual clutter (Remove e.g., "Page 1", duplicate titles, titles like 'Translated Arabic PDF', etc.).
5. Translate remaining Arabic if any.

--- Numbered Section Handling ---

 Do NOT turn numbered content into headings *just because it starts with a number*.

 ONLY convert numbered items to headings if:
- The number is clearly part of a **section heading** (e.g., "1. Basic Requirements").
- It is formatted like a section (e.g., <li><h2>...</h2></li>), with **no body content** or description inside the <li>.
- The number and title are meant to introduce a **distinct section**, not a bullet point, condition, or requirement.

 DO NOT promote numbered items to <h2>/<h3> if:
- The item is part of a list of tasks, features, conditions, or requirements.
- The item contains body text, descriptions, or child elements like <ul>, <table>, or <p>.
- The structure reads like a list, not a document outline.

 Keep these as normal lists using:
<ol>
  <li>First requirement</li>
  <li>Second requirement</li>
</ol>

 To determine intent:
- If it looks like a **long numbered checklist or conditions list**, it stays a list.
- If it's a section outline like: "1. Introduction", "2. Scope", "3. Implementation" — then use headings.



--- Output ---
Return only the cleaned, valid HTML with no markdown or extra wrapping.
"""

    messages = [
        {"role": "system", "content": SYSTEM_PROMPT.strip()},
        {"role": "user", "content": translated_html}
    ]

    response = client.chat.complete(
        model=FORMATTING_MODEL,
        messages=messages,
        temperature=0.0,
    )
    formatted_html = response.choices[0].message.content.strip()
    return formatted_html






# --- Batched HTML translation with retry for Arabic ---
def translate_html_batched(raw_html, stream_callback=None):
    batches = batch_html_by_tokens(raw_html, TOKEN_LIMIT)
    print(f"[BATCH] Total HTML batches: {len(batches)}")
    translated_batches = []
    SYSTEM_PROMPT = """
You are a professional Arabic-to-English translator and HTML prompt engineer.

Objective:
Translate the given **Arabic HTML** into clear, accurate **English HTML**, preserving all **HTML tags**, **structure**, **layout**, and **formatting**.

Translation Rules:
1. Translate **only** the visible Arabic text. Do **not** translate any English.
2. Translate **all** Arabic — no Arabic words or phrases should remain unless they are untranslatable proper names or technical terms.
3. Do **not** summarize, omit, paraphrase, or add any content. Every part of the original Arabic text must be preserved faithfully in English.
4. Ensure fluent, idiomatic, professional English that retains the meaning and tone of the source.

Formatting & Structure Guidelines:
- **Preserve all HTML elements and attributes exactly as in the original** — do not remove, add, or reformat tags.
- Retain headings (`<h1>`–`<h4>`), paragraphs, lists (`<ul>`, `<ol>`, `<li>`), and tables (`<table>`, `<tr>`, `<td>`, `<th>`) as-is.
- Keep indentation, whitespace, and line breaks aligned with the original HTML for readability.
- Maintain HTML **hierarchy and nesting** accurately.

Semantic Layout Conventions (if modifying block-level structure in downstream processing):
- `<h1>` → Only one main document title, centered.
- `<h2>` → Section titles (e.g., A., B., C.)
- `<h3>` → Subsections
- `<h4>` → Nested subsections
- If a heading or paragraph appears **centered** visually, include `"alignment": "center"` in the metadata block.
- If a paragraph is **indented**, include `"indentation": true`.
- Use `"emphasis": true` only for sections that appear visually highlighted/emphasized (e.g., bold red text).

Constraints:
- Do **not** alter the HTML layout, structure, or style attributes.
- Do **not** output markdown, JSON, or any explanation—return only the **translated HTML** (valid, clean, and complete).
- **CRITICAL**: Do NOT wrap your output in markdown code blocks (
html or 
). Return the HTML directly without any markdown formatting.

Begin translation when ready.



You are an expert HTML formatter and structural fixer.

---

### Objective:

Review the entire translated HTML document and fix all **formatting**, **hierarchy**, and **structural inconsistencies** to ensure a clean, logical, and professional document layout.

---

### Instructions:

1. **Preserve all content** — do not add, summarize, or remove any text.
2. Fix all **heading hierarchy issues**:

   * Only one `<h1>` tag for the main title (at the top).
   * Use `<h2>` for top-level sections (e.g., A., B., C.).
   * Use `<h3>` for subsections and nested content under each `<h2>`.
   * `<h4>` may be used for further depth, if needed.
3. Convert **incorrect tags** like `<p>` or `<div>` used for section titles into semantic headings.
4. Ensure **clean indentation**, **nesting**, and **whitespace**.
5. Remove **duplicated elements** or misused formatting tags (e.g., `Page 1`, `<p><h1>text</h1></p>`, etc.).
6. Retain all **styling classes** and attributes unless they break structure.
7. If any Arabic text is found, translate it to English.
8. Output only the **clean HTML**. No markdown, explanations, or wrapping.

---

### Examples of Fixes You Should Apply:

#### ❌ Bad:

```html
<h2>Page 1</h2>
<p>B. Preparing the Cloud Environment</p>
<h1>D. Data Migration</h1>
<p><h1>Final Payments:</h1></p>
```

#### ✅ Good:

```html
<!-- Remove "Page 1" -->
<h2>B. Preparing the Cloud Environment</h2>
<h2>D. Data Migration</h2>
<h2>Final Payments:</h2>
```

---

#### ❌ Bad:

```html
<h1>[Title]</h1>
<h1>D. Data Migration</h1> <!-- Invalid: multiple h1s -->
```

#### ✅ Good:

```html
<h1>[Title]</h1>
<h2>D. Data Migration</h2>
```

---

#### ❌ Bad:

```html
<p><h1>Section Heading</h1></p>
```

#### ✅ Good:

```html
<h2>Section Heading</h2>
```

---

#### ❌ Bad:

```html
<p><strong>[subheading]</strong></p>
```

#### ✅ Good:

```html
<h3>[subheading]</h3>
```

---

### CRITICAL:

* **Do not change** any actual content or meaning — this is a **structure-only** correction pass.
* Fix tag misuse, not language or content unless it's Arabic (translate that to English).
* Output should be clean, consistent HTML with properly indented, nested, and labeled tags.

---
Note: The HTML tags in this document were automatically generated from Markdown based on original document structure. Treat them as strong indicators of layout semantics.

Begin when ready. Process the full HTML now.

"""
    for i, batch in enumerate(batches):
        tokens = estimate_tokens(batch)
        print(f"[BATCH] Translating HTML batch {i+1}/{len(batches)} (tokens: {tokens})")
        print(f"[BATCH] Batch {i+1} content (first 500 chars):\n{batch}\n{'-'*40}")
        translated_html = None
        max_retries = 3
        for attempt in range(1, max_retries+1):
            messages = [
                {"role": "system", "content": SYSTEM_PROMPT.strip()},
                {"role": "user", "content": batch}
            ]
            response = client.chat.complete(
                model=TRANSLATION_MODEL,
                messages=messages,
                temperature=0.0,
            )
            translated_html = response.choices[0].message.content.strip()
            print(f"[BATCH] Translated HTML for batch {i+1} (attempt {attempt}, first 500 chars):\n{translated_html[:500]}\n{'='*40}")
            if not contains_arabic(translated_html):
                break
            else:
                print(f"[RETRY] Batch {i+1} still contains Arabic after attempt {attempt}. Retrying...")
        if contains_arabic(translated_html):
            print(f"[WARN] Batch {i+1} still contains Arabic after {max_retries} attempts.")

        # Stream callback for real-time updates
        if stream_callback and translated_html:
            readable_text = extract_readable_text_from_html(translated_html)
            if readable_text.strip():
                stream_callback({
                    'batch_number': i + 1,
                    'total_batches': len(batches),
                    'text': readable_text,
                    'html': translated_html
                })

        translated_batches.append(translated_html)
    return '\n'.join(translated_batches)




def process_tables_for_responsive_layout(html_content):
    """Process HTML to wrap tables in containers and add appropriate classes for responsive layout"""
    from bs4 import BeautifulSoup

    soup = BeautifulSoup(html_content, 'html.parser')
    tables = soup.find_all('table')

    for table in tables:
        # Count columns by looking at the first row (header or data)
        first_row = table.find('tr')
        if first_row:
            columns = len(first_row.find_all(['th', 'td']))

            # Add appropriate class based on column count
            if columns >= 11:
                table['class'] = table.get('class', []) + ['extra-wide-table']
            elif columns >= 8:
                table['class'] = table.get('class', []) + ['wide-table']

            # Create table container wrapper
            table_container = soup.new_tag('div', **{'class': 'table-container'})
            table.wrap(table_container)

    return str(soup)

def generate_translated_html(raw_html, page_count, stream_callback=None):
    translated_html = translate_html_batched(raw_html, stream_callback)
    # Remove unwanted tags and their content
    import re
    formatted_html = format_translated_html_with_codestral_auto(translated_html,page_count)

    # Remove markdown code blocks that might have been added by the model
    cleaned_html = re.sub(r'^```html\s*', '', formatted_html, flags=re.MULTILINE)
    cleaned_html = re.sub(r'^```\s*', '', cleaned_html, flags=re.MULTILINE)
    cleaned_html = re.sub(r'\s*```$', '', cleaned_html, flags=re.MULTILINE)
    
    # Remove doctype
    cleaned_html = re.sub(r'<!DOCTYPE[^>]*>', '', cleaned_html, flags=re.IGNORECASE)
    # Remove <meta ...> and <title> tags
    cleaned_html = re.sub(r'<meta[^>]*>', '', cleaned_html, flags=re.IGNORECASE)
    cleaned_html = re.sub(r'<title[^>]*>.*?</title>', '', cleaned_html, flags=re.IGNORECASE | re.DOTALL)
    # Remove <style>...</style> blocks
    cleaned_html = re.sub(r'<style[^>]*>.*?</style>', '', cleaned_html, flags=re.IGNORECASE | re.DOTALL)
    # Remove <html>, <head>, <body> tags (already in your code)
    cleaned_html = re.sub(r'<\/?(html|head|body)[^>]*>', '', cleaned_html, flags=re.IGNORECASE)
    # Strip leading/trailing whitespace
    cleaned_html = cleaned_html.strip()

    # Process tables for responsive layout
    cleaned_html = process_tables_for_responsive_layout(cleaned_html)

    html_parts = [
        "<!DOCTYPE html>",
        "<html lang='en'>",
        "<head>",
        "<meta charset='UTF-8'>",
        "<meta name='viewport' content='width=device-width, initial-scale=1.0'>",
        "<title>Translated Document</title>",
        "<style>",
        """
        body {
            font-family: 'Segoe UI', Arial, Helvetica, Tahoma, Geneva, Verdana, sans-serif;
            font-size: 17px;
            line-height: 1.8;
            color: #222;
            margin: 0;
            padding: 0;
            background: #f8fafd;
            direction: ltr;
        }
        .document-container {
            padding: 48px 32px 48px 32px;
            max-width: 900px;
            margin: 40px auto 40px auto;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.08);
        }
        h1 {
            font-size: 2.8em;
            font-weight: 700;
            color: #003366;
            text-align: center;
            margin-top: 0;
            margin-bottom: 0.7em;
            letter-spacing: 0.02em;
        }
        h2 {
            font-size: 1.7em;
            color: #003366;
            font-weight: 600;
            margin-top: 2.5em;
            margin-bottom: 0.7em;
            border-bottom: 2px solid #e0e6ed;
            padding-bottom: 6px;
            letter-spacing: 0.01em;
        }
        h3 {
            font-size: 1.25em;
            color: #00509e;
            font-weight: 600;
            margin-top: 2em;
            margin-bottom: 0.5em;
        }
        h4, h5, h6 {
            font-size: 1.1em;
            color: #003366;
            font-weight: 500;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        p {
            margin: 18px 0;
            color: #333;
            text-align: justify;
            text-justify: inter-word;
        }
        ul, ol {
            margin: 18px 0 18px 36px;
            padding-left: 1.2em;
        }
        ul {
            list-style-type: disc;
        }
        ol {
            list-style-type: decimal;
        }
        ul ul, ol ul {
            list-style-type: circle;
            margin-top: 0;
            margin-bottom: 0;
        }
        ul ol, ol ol {
            list-style-type: lower-latin;
            margin-top: 0;
            margin-bottom: 0;
        }
        li {
            margin-bottom: 0.4em;
            font-size: 1em;
        }
        /* Table container with horizontal scroll for wide tables */
        .table-container {
            width: 100%;
            overflow-x: auto;
            margin: 32px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.07);
            background: #fff;
        }

        table {
            width: 100%;
            min-width: 800px; /* Minimum width to prevent cramping */
            border-collapse: collapse;
            margin: 0;
            background: #fff;
        }

        /* Responsive table sizing based on column count */
        table.wide-table {
            min-width: 1200px; /* For tables with 8+ columns */
        }

        table.extra-wide-table {
            min-width: 1600px; /* For tables with 11+ columns */
        }

        table th, table td {
            border: 1px solid #d1dbe6;
            padding: 12px 8px; /* Reduced padding for wide tables */
            text-align: left;
            vertical-align: top;
            white-space: nowrap; /* Prevent text wrapping in headers */
            min-width: 80px; /* Minimum column width */
        }

        /* Allow text wrapping in description columns */
        table td:nth-child(5), /* Item Description */
        table td:nth-child(6)  /* Specifications */ {
            white-space: normal;
            max-width: 200px;
            min-width: 150px;
        }

        table th {
            background-color: #003366;
            color: #fff;
            font-weight: 700;
            font-size: 1em; /* Slightly smaller for wide tables */
            position: sticky;
            top: 0;
            z-index: 10;
        }

        table tr:nth-child(even) td {
            background-color: #f4f8fb;
        }

        table tr:hover td {
            background-color: #e6f0fa;
        }
        @media (max-width: 1200px) {
            .document-container {
                max-width: 95vw; /* Allow wider container for large tables */
            }
        }

        @media (max-width: 700px) {
            .document-container {
                padding: 16px 4px;
                max-width: 100vw;
            }
            h1 { font-size: 2em; }
            h2 { font-size: 1.2em; }

            /* Mobile table adjustments */
            table th, table td {
                padding: 8px 4px;
                font-size: 0.9em;
            }

            table th {
                font-size: 0.85em;
            }

            /* Reduce minimum widths on mobile */
            table {
                min-width: 600px;
            }

            table.wide-table {
                min-width: 900px;
            }

            table.extra-wide-table {
                min-width: 1200px;
            }
        }
        @media print {
            body, .document-container {
                background: #fff !important;
                box-shadow: none !important;
            }
            .document-container {
                padding: 0;
                margin: 0;
            }
        }
        """,
        "</style>",
        "</head>",
        "<body>",
        "<div class='document-container'>",
        cleaned_html,
        "</div>",
        "</body>",
        "</html>"
    ]
    return '\n'.join(html_parts)

# Utilities
def load_html(path):
    with open(path, "r", encoding="utf-8") as f:
        return f.read()

def save_html(output_html, path):
    with open(path, "w", encoding="utf-8") as f:
        f.write(output_html)
        


# --- Exported function for pipeline use ---
def translate_html_file(input_html_path, output_html_path, page_count, stream_callback=None):
    print(f"[translate_html_file] Translating {input_html_path} -> {output_html_path}")
    raw_html = load_html(input_html_path)
    translated_html = generate_translated_html(raw_html, page_count, stream_callback)
    save_html(translated_html, output_html_path)

    return page_count

def format_translated_html_with_codestral_auto(translated_html, page_count):
    if page_count is not None and page_count <= 30:
        print("[CODESTRAL] Small document — using single-shot formatter.")
        return format_translated_html_with_codestral(translated_html)
    else:
        print("[CODESTRAL] Large document detected — using chunked formatter.")
        return format_translated_html_with_codestral_chunked(translated_html)


# Main
# if __name__ == "__main__":
#     start_total = time.time()

#     print("→ Loading HTML...")
#     raw_html = load_html(INPUT_HTML_PATH)

#     # Timing Saba translation
#     print("→ Translating content via Mistral (Saba)...")
#     start_saba = time.time()
#     translated_html = translate_html_batched(raw_html)
#     end_saba = time.time()
#     print(f"⏱️ Saba Translation Time: {end_saba - start_saba:.2f} seconds")

#     # Timing Codestral formatting
#     print("→ Formatting translated HTML via Codestral...")
#     start_codestral = time.time()
#     formatted_html = format_translated_html_with_codestral_auto(translated_html,page_count)
#     end_codestral = time.time()
#     print(f"⏱️ Codestral Formatting Time: {end_codestral - start_codestral:.2f} seconds")

#     # Final cleanup and wrapping
#     print("→ Cleaning and wrapping HTML...")
#     cleaned_html = formatted_html  # Optionally call cleanup functions here
#     save_html(cleaned_html, OUTPUT_HTML_PATH)

#     end_total = time.time()
#     print(f"✅ Done! Translated HTML saved to: {OUTPUT_HTML_PATH}")
#     print(f"⏱️ Total Pipeline Time: {end_total - start_total:.2f} seconds")

