import React, { useState, useEffect, useRef, useCallback } from 'react';

interface TypewriterTextProps {
  text: string;
  speed?: number; // Characters per second
  onComplete?: () => void;
  className?: string;
}

const TypewriterText: React.FC<TypewriterTextProps> = ({
  text,
  speed = 50, // Default 50 characters per second
  onComplete,
  className = ''
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const onCompleteRef = useRef(onComplete);

  // Update the ref when onComplete changes, but don't restart the effect
  useEffect(() => {
    onCompleteRef.current = onComplete;
  }, [onComplete]);

  // Stable callback that uses the ref
  const handleComplete = useCallback(() => {
    if (!isCompleted) {
      setIsCompleted(true);
      onCompleteRef.current?.();
    }
  }, [isCompleted]);

  useEffect(() => {
    // Reset when text changes
    setDisplayedText('');
    setCurrentIndex(0);
    setIsCompleted(false);

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (text.length === 0) return;

    const intervalDelay = 1000 / speed; // Convert speed to interval delay

    intervalRef.current = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        if (prevIndex >= text.length) {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
          // Use setTimeout to ensure state updates are complete before calling onComplete
          setTimeout(() => handleComplete(), 0);
          return prevIndex;
        }

        const nextIndex = prevIndex + 1;
        setDisplayedText(text.slice(0, nextIndex));
        return nextIndex;
      });
    }, intervalDelay);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [text, speed, handleComplete]); // Removed onComplete from dependencies

  return (
    <div className={className}>
      {displayedText}
      {currentIndex < text.length && (
        <span className="animate-pulse text-cyan-400">|</span>
      )}
    </div>
  );
};

export default TypewriterText;
